package com.hailiang.composition.ui.practice

import android.app.Application
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.SystemClock
import androidx.lifecycle.viewModelScope
import com.bumptech.glide.Glide
import com.hailiang.camera.pictureselector.entity.HLLocalMedia
import com.hailiang.camera.pictureselector.entity.HLSelectMimeType
import com.hailiang.common.download.resource.WorkResource
import com.hailiang.common.download.resource.WorkResourceDownload
import com.hailiang.common.util.BitmapUtil
import com.hailiang.composition.data.AiStreamClient
import com.hailiang.composition.data.Repository
import com.hailiang.composition.data.bean.AiStreamChunkResponse
import com.hailiang.composition.data.bean.AiStreamDetail
import com.hailiang.composition.data.bean.AiStreamType
import com.hailiang.composition.data.bean.CompositionCheckBean
import com.hailiang.composition.data.bean.CompositionStatusResponse
import com.hailiang.composition.data.bean.WorkDetail
import com.hailiang.composition.data.bean.WorkStatus
import com.hailiang.composition.data.bean.request.ArticleType
import com.hailiang.composition.data.bean.request.EvaluateType
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.composition.log.BusinessBury
import com.hailiang.composition.mediatools.MediaImage
import com.hailiang.composition.ui.CompositionHelper
import com.hailiang.composition.ui.practice.AiWorkFlowManager.AiFlow
import com.hailiang.core.base.BaseAndroidViewModel
import com.hailiang.core.ext.launchOnHttp
import com.hailiang.core.ext.launchWithException
import com.hailiang.core.ext.set
import com.hailiang.core.thread.ThreadPlugins
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.JsonUtil
import com.hailiang.hlutil.ext.jsonToList
import com.hailiang.hlutil.getApplication
import com.hailiang.hlutil.launchCatch
import com.hailiang.ui.designsystem.toast.ToastUtils
import com.hailiang.workcloud.data.vo.CompositionCheckDetail
import com.hailiang.workcloud.download.resource.ResourceDownloadCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.update
import java.io.File

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/16 00:15
 */
class CompositionViewModel(application: Application) : BaseAndroidViewModel(application) {
    private val compositionRepository = Repository()
    var curWorkStateId = -1L
    var curWorkId = -1L

    //
    private var stepManager = CompositionStepManager(WorkDetail.empty())
    val compositionStepState = MutableStateFlow<CompositionStep>(CompositionStep.Default)
    val isShowKeyBoard = MutableStateFlow<Boolean>(true)
    //
    /**
     * 作文题目、材料信息
     */
    val compositionContentState = MutableStateFlow(CompositionContentState())

    /**
     * 学生答案图片信息
     */
    val studentAnswerBitmapState =
        MutableStateFlow<CompositionStudentAnswersBitmapState>(CompositionStudentAnswersBitmapState.Loading)
    /**
     * 作文题目图片信息
     */
    val compositionQuestionImagesState =
        MutableStateFlow<CompositionQuestionImageState>(CompositionQuestionImageState.Loading)

    /**
     * 二次作答练习内容 + AI批改信息
     */
    val aiResponseState = MutableStateFlow<AiResponseState>(AiResponseState.Default)
    val scoreState = MutableStateFlow(-1)

    val sentenceClickIndex = MutableStateFlow(-1)
    private var stepStartTime = -1L

    /**
     * 题目识别结果
     */
    @Deprecated("已废弃")
    val topicOcrResponseState = MutableStateFlow<AiResponseState>(AiResponseState.Default)

    val compositionTableState =
        MutableStateFlow<CompositionTableState>(CompositionTableState.Loading)

    fun observeAiSuccessOnly() = aiResponseState
        .filter { it is AiResponseState.Success }
        .distinctUntilChanged()

    /**
     * 学生点赞状态
     */
    val studentFeedbackState =
        MutableStateFlow<EvaluateFeedbackState>(
            EvaluateFeedbackState(
                feedbackMap = emptyMap(),
                needAnimation = false
            )
        )

    val studentSecondFeedbackState =
        MutableStateFlow<EvaluateFeedbackState>(
            EvaluateFeedbackState(
                feedbackMap = emptyMap(),
                needAnimation = false
            )
        )
    // ----------------------------------------------------------------------
    /**
     * 综合评价
     */
    private var comprehensiveJudge: CompositionCheckBean.ComprehensiveJudge? = null

    /**
     * 点拨
     */
    private var adviceList: List<CompositionCheckBean.Advice>? = null

    /**
     * 典故列表（知识加油站）
     */
    private var allusionList: List<CompositionCheckBean.Allusion>? = null

    // ----------------------------------------------------------------------
    private var cachedSelectedAiSector: AiSector = AiSector.Evaluate
    private var cachedAiStreamJob: Job? = null
    private var aiStepStartTimeMillis = -1L

    // ----------------------------------------------------------------------
    private val waitingAiJudgeResultTask = Runnable {
        requestAiFirstCorrectInfo()
    }
    private val waitingAiBackgroundResultTask = Runnable {
        requestAiBackgroundJob()
    }
    private val requestAiStreamStatusTask = Runnable {
        actualRequestAiStreamStatus()
    }
    // ----------------------------------------------------------------------
    /**
     * OCR 识别开始时间
     */
    private var ocrCreateTimeMillis = 0L

    private val ocrLoadingTask = Runnable {
        val state = aiResponseState.value
        val topicState = aiResponseState.value
        if (state !is AiResponseState.OcrLoading && topicState !is AiResponseState.OcrLoading) {
            return@Runnable
        }
        if ((state as? AiResponseState.OcrLoading)?.isTopic == false) {
            updateOcrProgress(AiStreamType.First.value)
        } else {
            updateOcrProgress(AiStreamType.Title.value)
        }
    }

    // ----------------------------------------------------------------------
    private val aiStreamClient by lazy {
        AiStreamClient(dataParser = {
            try {
                // 使用流式解析替代一次性解析大JSON
                JsonUtil.parseJson(it, AiStreamChunkResponse::class.java)
            } catch (e: OutOfMemoryError) {
                HLog.e(HTag.TAG, "JSON解析内存不足: ${e.message}")
                null
            } catch (e: Exception) {
                HLog.e(HTag.TAG, "JSON解析错误: ${e.message}")
                null
            }

        })
    }

    private val aiWorkFlowManager by lazy {
        AiWorkFlowManager()
    }

    @OptIn(FlowPreview::class)
    val aiWorkFlow: SharedFlow<AiFlow> = aiWorkFlowManager.aiFlowState
        .sample(300L)
        .onEach { flow ->
            switchStep(stepManager.updateStepWhenAiStatusChanged(flow))
            when (flow) {
                is AiFlow.TitleOcrPreparing -> {
                    checkOcrJob(flow.ocrCreateTimeMillis, AiStreamType.Title.value)
                }

                is AiFlow.TitleOrcFailed -> {
                    ocrJobFailed(flow.compositionStatusResponse, flow.errorMessage, AiStreamType.Title.value)

//                    ocrJobFailed(flow.errorMessage, AiStreamType.Title.value)
                }

                is AiFlow.TitleOcrSuccess -> {
                    finishOcrJob(AiStreamType.Title.value)
                }
                //
                is AiFlow.ContentOcrPreparing -> {
                    checkOcrJob(flow.ocrCreateTimeMillis, AiStreamType.First.value)
                }

                is AiFlow.StreamPreparing -> {
                    if (flow.streamType == AiStreamType.Second && !stepManager.isAllSubmitted()) {
                        // 二流准备中，且作业未提交，请求一下批改结果，展示一搞AI批改结果
                        ThreadPlugins.runOnWorkThread(waitingAiJudgeResultTask, 1_000L)
                    } else {
                        // 继续处理二稿流
                        ThreadPlugins.runOnWorkThread(requestAiStreamStatusTask, 3_000L)
                    }
                }

                is AiFlow.OcrFailed -> ocrJobFailed(
                   null,
                    flow.errorMessage,
                    AiStreamType.First.value
                )
                //
//                is AiFlow.TopicStreamReady -> {
//                    finishOcrJob(AiStreamType.First.value)
//                }
//
//                is AiFlow.FirstStreamReady, is AiFlow.SecondStreamReady -> {
//                }
                //
                is AiFlow.Streaming -> {
                    updateAiJudgeState(AiResponseState.AiStreaming(flow.streamDetail))
                    if (flow.streamDetail.isCompletion()) {
                        if (flow.streamType == AiStreamType.Title) {
                            ThreadPlugins.runOnWorkThread(requestAiStreamStatusTask, 1_000L)
                        } else {
                            ThreadPlugins.runOnWorkThread(waitingAiJudgeResultTask, 1_000L)
                        }
                    }
                }

                is AiFlow.StreamSuccess -> {
                    if (flow.streamType == AiStreamType.Title) {
                        // 需要继续处理一流
                        ThreadPlugins.runOnWorkThread(requestAiStreamStatusTask, 3_000L)
                    }
                }

                is AiFlow.StreamError ->
                    updateAiJudgeState(
                        AiResponseState.AiStreamError(
                            AiStreamDetail.obtainErrorDetail()
                        )
                    )

                is AiFlow.RequestError -> {
                    updateAiJudgeState(
                        if (flow.streamDetail.isEmpty()) {
                            AiResponseState.Reload
                        } else {
                            AiResponseState.AiRequestError(flow.streamDetail)
                        }
                    )
                }
                //
                is AiFlow.AllSuccess -> {
                    updateCompositionOcrState(CompositionTableState.Loading)
                    showOcrAndAiJudgeState(flow.aiCheckContent)
                }

                is AiFlow.BackgroundRunning -> {
                    updateCompositionOcrState(CompositionTableState.Loading)
                    showOcrAndAiJudgeState(flow.aiCheckContent)
                    requestAiBackgroundJob()
                }

                else -> Unit // 其他类型不做处理
            }
        }.shareIn(viewModelScope, SharingStarted.Companion.Lazily, replay = 1)


    /**
     * 加载学生作答图片
     */
    val studentAnswerImages: Flow<CompositionStudentAnswersState>
        get() = compositionRepository.queryCompositionWorkAnswerWithFlow(curWorkId)
            .map { compositionWorkAnswer ->
                //        listOf(
//            "https://mmbiz.qpic.cn/mmbiz_jpg/KvXbrRs6BAWpcuPfQDQHRM81icwLpJRlne0qWpyf2PEiapbjp8aCdOiaYcAtm4NJbWxaXEjNxMSWS9QjfBvV5FoIQ/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1",
//            "https://mmbiz.qpic.cn/mmbiz_jpg/KvXbrRs6BAWpcuPfQDQHRM81icwLpJRlne0qWpyf2PEiapbjp8aCdOiaYcAtm4NJbWxaXEjNxMSWS9QjfBvV5FoIQ/640?wx_fmt=jpeg&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1"
//        )
                HLog.i(HTag.TAG, "调用了这个？？？？？？？？？")
                updateStudentImageState(CompositionStudentAnswersBitmapState.Loading)
                HLog.i(
                    HTag.TAG,
                    "加载学生作答图片信息: curWorkId: $curWorkId; ${compositionWorkAnswer}"
                )
                val resourceList =
                    compositionWorkAnswer?.imageAnswerList?.jsonToList(String::class.java)?.map {
                        WorkResource.createImageResource(url = it)
                    } ?: emptyList()
                val compositionStudentAnswersState = CompositionStudentAnswersState(
                    imageResources = resourceList,
                    mediaImageList = resourceList.map {
                        val localPath = it.getLocalPath()
                        MediaImage(
                            state = MediaImage.STATE_SUCCESS,
                            media = HLLocalMedia(
                                0,
                                0,
                                0,
                                localPath,
                                HLSelectMimeType.SYSTEM_IMAGE,
                                localPath
                            ),
                            url = it.url
                        )
                    }
                )
                compositionStudentAnswersState
            }.flowOn(
                ThreadPlugins.ioDispatcher()
            ).shareIn(viewModelScope, SharingStarted.Lazily, replay = 1)

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    fun init(workId: Long, workStateId: Long) {
        curWorkId = workId
        curWorkStateId = workStateId
    }

    /**
     * 加载作文作业信息
     */
    fun loadCompositionWork() {
        viewModelScope.launchCatch(errorBlock = { finish.set() }) {
            val workDetail = compositionRepository.requestCompositionInfo(
                curWorkId, curWorkStateId
            )
            if (workDetail == null) {
                ToastUtils.showShort("作业信息加载失败!")
                finish.set()
                return@launchCatch
            }
            stepManager = CompositionStepManager(workDetail)
            compositionContentState.value = CompositionContentState(
                workId = curWorkId,
                workStateId = curWorkStateId,
                workDetail = workDetail
            )
            downloadAndCombineQuestionImages(workDetail)
            // 作业材料
//            val compositionMaterials =
//                compositionRepository.queryCompositionMaterials(workId = curWorkId)
//            compositionContentState.update {
//                it.copy(
//                    workId = curWorkId,
//                    workStateId = curWorkStateId,
//                    title = workDetail.getSchoolworkName(),
//                    questionContent = workDetail.getRemark(),
//                    materialList = compositionMaterials,
//                    endTime = deadline,
//                )
//            }

        }
    }

    fun loadStep() {
        switchStep(stepManager.currentStep)
    }

    fun switchStep(newStep: CompositionStep) {
//        HLog.i(HTag.TAG, "switchStep: $newStep")
        val switchTo = stepManager.switchStep(newStep)
        if (compositionStepState.value != switchTo) {
            reportSwitchStepEvent()
            reportAiTabSwitchEvent()
            resetAiStepTimeCounting(switchTo) // 根据新Step重置一下
            compositionStepState.value = switchTo
        }
    }

    /**
     * 去二次作答
     */
    fun toSecondPractice() {
        switchStep(CompositionStep.SecondPractice)
    }

    /**
     * 显示AI批改内容
     */
    fun toAiGuidance() {
        switchStep(CompositionStep.AiGuidance)
    }

    fun startCounting() {
        saveAndRestCounting()
    }

    private var timeCountStart: Long = Long.MAX_VALUE

    fun saveAndRestCounting() {
        val offset = SystemClock.uptimeMillis() - timeCountStart
        timeCountStart = SystemClock.uptimeMillis()
        if (offset > 0) {
            stepManager.addTimeCounting(offset)
        }
    }
    // ----------------------------------------------------------------------
    fun downloadAndCombineQuestionImages(workDetail: WorkDetail) {
        val taskList = workDetail.taskList;
        HLog.i(HTag.TAG, "下载题目图片: $taskList")
        if (taskList.isNullOrEmpty()) {
            updateQuestionImageState(CompositionQuestionImageState.Success(combineImage = null))
            return
        }
        updateQuestionImageState(CompositionQuestionImageState.Loading)
        val resources = mutableListOf<String>().apply {
            taskList.forEach { taskBean ->
                taskBean.taskMaterialList?.forEach { materialBean ->
                    if (materialBean.materialFileType == "picture" &&
                        !materialBean.materialFileUrl.isNullOrEmpty()
                    ) {
                        add(materialBean.materialFileUrl)
                    }
                }
            }
        }
        HLog.i(HTag.TAG, "需要下载资源图片: $resources")
        combineQuestionImages(resources)

    }

    fun downloadAndCombineStudentAnswers(resourceList: List<WorkResource>?, size: Int = 800) {
        viewModelScope.launchWithException(ThreadPlugins.ioDispatcher()) {
            HLog.i(HTag.TAG, "检查作文作业资源: $resourceList")
            if (resourceList.isNullOrEmpty()) {
                updateStudentImageState(
                    CompositionStudentAnswersBitmapState.Success(
                        combineImage = null,
                    )
                )
                return@launchWithException
            }
            updateStudentImageState(CompositionStudentAnswersBitmapState.Loading)
            val needDownloadResourceList = resourceList.filter {
                !it.resourceExists() && BitmapFactory.decodeFile(it.getLocalPath()) == null
            }
            if (needDownloadResourceList.isEmpty()) {
                HLog.i(HTag.TAG, "资源本地已都存在，不用下载")
                combineStudentAnswerResources(resourceList, size)
                return@launchWithException
            }
            HLog.i(HTag.TAG, "需要下载资源图片: $needDownloadResourceList")
            WorkResourceDownload.downloadResources(
                resources = needDownloadResourceList,
                downloadListener = object : ResourceDownloadCallback {
                    override fun onStartDownload(workResource: WorkResource) {
                    }

                    override fun onFinishDownload(
                        workResource: WorkResource,
                        savedFile: String?,
                    ) {
                    }

                    override fun onFail(workResource: WorkResource, errorInfo: String?) {
                      HLog.e(HTag.TAG, "下载失败: $workResource")
                        HLog.e(HTag.TAG, "下载失败原因: $errorInfo")
                    }

                    override fun onAllComplete() {
                        combineStudentAnswerResources(resourceList, size)
                    }
                })
        }
    }

    private fun combineStudentAnswerResources(resources: List<WorkResource>, size: Int) {
        combineStudentAnswerImages(resources.map { it.getLocalPath() }, size)
    }

    fun combineStudentAnswerImages(resources: List<String>, size: Int = 800) {
        HLog.i(HTag.TAG, "合并学生作答图片: $resources")
        viewModelScope.launchCatch(Dispatchers.IO) {
            val bitmapList = mutableListOf<Bitmap>()
            resources.mapNotNull { url ->
                HLog.i(HTag.TAG, "加载学生作答图片 url: $url")
                processBitmap(Glide.with(getApplication()).asBitmap().load(url).submit().get())
            }.forEach {
                bitmapList.add(it)
            }

            val combineImage = BitmapUtil.mergeBitmaps(
                bitmapList, size
            )
            updateStudentImageState(
                CompositionStudentAnswersBitmapState.Success(
                    combineImage = combineImage,
                )
            )
        }
    }
    fun combineQuestionImages(resources: List<String>, size: Int = 800) {
        viewModelScope.launchCatch(Dispatchers.IO) {
            val bitmapList = mutableListOf<Bitmap>()
            resources.forEach { url ->
                try {
                    if (url.startsWith("http")) {
                        // 处理远程URL
                        processBitmap(
                            Glide.with(getApplication()).asBitmap().load(url).submit().get()
                        )?.let { bitmapList.add(it) }
                    } else {
                        // 处理本地路径
                        val file = File(url)
                        if (file.exists()) {
                            processBitmap(
                                Glide.with(getApplication()).asBitmap().load(file).submit().get()
                            )?.let { bitmapList.add(it) }
                        } else {
                            HLog.e(HTag.TAG, "本地图片文件不存在: $url")
                        }
                    }
                } catch (e: Exception) {
                    HLog.e(HTag.TAG, "加载图片失败: $url", e)
                }
            }

            val combineImage = if (bitmapList.isNotEmpty()) {
                BitmapUtil.mergeBitmaps(bitmapList, size)
            } else {
                null
            }
            HLog.i(HTag.TAG, "合并题目图片完成: $combineImage")
            updateQuestionImageState(
                CompositionQuestionImageState.Success(
                    combineImage = combineImage,
                )
            )
        }
    }

    private fun processBitmap(bitmap: Bitmap?): Bitmap? {
        // TODO AI暂不支持
        return bitmap
    }

    private fun updateStudentImageState(studentAnswersBitmapState: CompositionStudentAnswersBitmapState) {
        val stackTrace = Thread.currentThread().stackTrace
        val callerInfo = stackTrace.getOrNull(3)?.let { "${it.fileName}:${it.lineNumber}" } ?: "Unknown"
        HLog.i(HTag.TAG, "更新学生作答图片: $studentAnswersBitmapState, 调用来源: $callerInfo")
        studentAnswerBitmapState.value = studentAnswersBitmapState
    }

    private fun updateQuestionImageState(questionImageState: CompositionQuestionImageState) {
//        HLog.i(HTag.TAG, "更新题目图片: $questionImageState")
        compositionQuestionImagesState.value = questionImageState
    }

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    fun requestAiStreamStatus() {
        ThreadPlugins.runOnWorkThread(requestAiStreamStatusTask, 0)
    }

    private fun actualRequestAiStreamStatus() {
        viewModelScope.launchOnHttp(onError = {
            updateAiJudgeState(AiResponseState.Reload)
        }) {
            if (curWorkId <= 0 || curWorkStateId <= 0) {
                ToastUtils.showShort("作业状态错误!")
                finish.set()
                return@launchOnHttp
            }
            aiWorkFlowManager.requestAiStreamStatus(
                workId = curWorkId,
                workStateId = curWorkStateId
            )
        }
    }

    fun reloadAiStream() {
        HLog.i(HTag.TAG, "reloadAiStream")
        updateAiJudgeState(AiResponseState.Loading)
        ThreadPlugins.runOnWorkThread(requestAiStreamStatusTask, 1_000L)
    }

    fun retryAiStream() {
        HLog.i(HTag.TAG, "retryAiStream")
        viewModelScope.launchOnHttp(onError = {
            updateAiJudgeState(AiResponseState.Retry)
        }) {
            updateAiJudgeState(AiResponseState.Loading)
            delay(1_000L)
            aiWorkFlowManager.retryAiStream(workId = curWorkId, workStateId = curWorkStateId)
        }
    }

    /**
     * 请求AI批改结果
     */
    fun requestAiFirstCorrectInfo() {
        if (curWorkId <= 0 || curWorkStateId <= 0) {
            ToastUtils.showShort("作业状态错误!")
            finish.set()
            return
        }
        viewModelScope.launchOnHttp(onError = {
            updateAiJudgeState(AiResponseState.Reload)
        }) {
            aiWorkFlowManager.requestAiCheckInfo(workId = curWorkId, workStateId = curWorkStateId)
        }
    }

    private fun requestAiBackgroundJob() {
        viewModelScope.launchOnHttp(
            onError = {
                updateAiJudgeState(AiResponseState.Reload)
            }
        ) {
            val aiCheckContent = compositionRepository.requestAiCheckInfo(
                workId = curWorkId,
                workStateId = curWorkStateId,
                isSubmitted = stepManager.isAllSubmitted()
            )
            if (aiCheckContent == null) {
                updateAiJudgeState(AiResponseState.Reload)
                return@launchOnHttp
            }
            val scoreJobStatus = aiCheckContent.getScoreJobStatus()
            val allusionJobStatus = aiCheckContent.getAllusionJobStatus()
            val aiJudgeResult = aiCheckContent.getAiJudgeJobStatus()
            val preAiResponseState = aiResponseState.value
            when {
                scoreJobStatus.isRunning() -> {
                    HLog.i(HTag.TAG, "分数计算中~~")
                    ThreadPlugins.runOnWorkThread(waitingAiBackgroundResultTask, 3_000L)
                }

                aiJudgeResult.isRunning() -> {
                    HLog.i(HTag.TAG, "综合评价、点拨生成中~~")
                    ThreadPlugins.runOnWorkThread(waitingAiBackgroundResultTask, 3_000L)
                }

                allusionJobStatus.isRunning() -> {
                    HLog.i(HTag.TAG, "加油站生成中~~")
                    if (aiCheckContent.isSubmitted != true) {
                        ThreadPlugins.runOnWorkThread(waitingAiBackgroundResultTask, 3_000L)
                    }
                }

                else -> { // 成功
                    showOcrAndAiJudgeState(
                        compositionRepository.queryAiFirstCheckDetail(
                            workId = curWorkId,
                            workStateId = curWorkStateId
                        )
                    )
                    return@launchOnHttp
                }
            }
            // 存在 loading，并且 分数或者加油站状态发生了变化，刷新一下
            if (preAiResponseState is AiResponseState.Success
                && (preAiResponseState.scoreJobStatus != scoreJobStatus
                        || preAiResponseState.allusionJobStatus != allusionJobStatus)
            ) {
                showOcrAndAiJudgeState(
                    compositionRepository.queryAiFirstCheckDetail(
                        workId = curWorkId,
                        workStateId = curWorkStateId
                    )
                )
            }
            return@launchOnHttp
        }
    }

    private fun showOcrAndAiJudgeState(
        aiCheckDetail: CompositionCheckDetail?,
    ) {
        //
        this.comprehensiveJudge = aiCheckDetail?.comprehensiveJudge
        this.adviceList = aiCheckDetail?.adviceList
        this.allusionList = aiCheckDetail?.allusionList

        updateCompositionDiffState()
        if (aiStepStartTimeMillis < 0) {
            aiStepStartTimeMillis = System.currentTimeMillis()
        }
        updateAiJudgeState(
            AiResponseState.Success(
                allJobStatus = JobStatus.getEnumByValue(aiCheckDetail?.jobStatus),
                scoreJobStatus = aiCheckDetail?.scoreJobStatus ?: JobStatus.FAILED,
                allusionJobStatus = aiCheckDetail?.allusionJobStatus ?: JobStatus.FAILED,
                selectedAiSector = cachedSelectedAiSector,
                aiSubSectorList = getAiSubSectorList(cachedSelectedAiSector),
                comprehensiveJudge = comprehensiveJudge,
                adviceList = adviceList,
                allusionList = allusionList,
                score = aiCheckDetail?.score ?: 0,
            )
        )
        scoreState.value = aiCheckDetail?.score ?: -1
    }

    private fun updateCompositionDiffState() {
        val composition = CompositionHelper.obtainCompositionTableBean(
            compositionRepository.queryCompositionPracticeInfo(
                workId = curWorkId,
                workStateId = curWorkStateId
            )
        )
        val firstCheckDetail = compositionRepository.queryAiFirstCheckDetail(
            curWorkId, curWorkStateId
        )
        updateCompositionOcrState(
            CompositionTableState.Success(
                ocrTitle = firstCheckDetail?.ocrTitle,
                ocrContent = firstCheckDetail?.ocrContent,
                composition = composition,
                needDoCompare = stepManager.isAllSubmitted()
            )
        )
    }

    private fun getAiSubSectorList(aiSector: AiSector): List<AiSubSector> {
        return aiSector.getAiSubSectorList(
            comprehensiveJudge = comprehensiveJudge,
            adviceList = adviceList,
            allusionList = allusionList
        )
    }


    // ----------------------------------------------------------------------
    /**
     * 切换AI内容的tab：综合评价、点拨、加油站
     */
    fun switchAiTab(compositionStep: CompositionStep, aiSector: AiSector) {
        HLog.i(HTag.TAG, "switchAiTab: $aiSector")
        refreshFeedbackEvaluate(aiSector, false, ArticleType.FirstDraft)
        refreshFeedbackEvaluate(aiSector, false, ArticleType.SecondDraft)
        viewModelScope.launchWithException {
            reportAiTabSwitchEvent(compositionStep, cachedSelectedAiSector) // 上报之前的
            val aiCorrectState = aiResponseState.value
            if (aiCorrectState is AiResponseState.Success) {
                cachedSelectedAiSector = aiSector
                updateAiJudgeState(
                    aiCorrectState.copy(
                        selectedAiSector = aiSector,
                        aiSubSectorList = getAiSubSectorList(aiSector)
                    )
                )
            }
        }
    }

    // ----------------------------------------------------------------------
    /**
     * 更新AI批改状态
     */
    private fun updateAiJudgeState(aiResponseState: AiResponseState) {
//        HLog.i(HTag.TAG, "刷新AI识别结果 aiResponseState: ${aiResponseState.javaClass.simpleName}")
        if (aiResponseState is AiResponseState.Success) {
            refreshFeedbackEvaluate(aiResponseState.selectedAiSector, false, ArticleType.FirstDraft)
            refreshFeedbackEvaluate(aiResponseState.selectedAiSector, false, ArticleType.SecondDraft)
        }
        this.aiResponseState.value = aiResponseState
    }

    private fun updateCompositionOcrState(state: CompositionTableState) {
        HLog.Companion.i(HTag.TAG, "显示Ocr识别结果 state: $state")
        this.compositionTableState.value = state
    }

    fun stopLoopAiResult() {
        ThreadPlugins.removeWorkCallbacks(requestAiStreamStatusTask)
        ThreadPlugins.removeWorkCallbacks(waitingAiJudgeResultTask)
        ThreadPlugins.removeWorkCallbacks(waitingAiBackgroundResultTask)
    }
    // ----------------------------------------------------------------------
    /**
     * 初稿提交反馈
     */
    fun firstDraftFeedback(
        aiSector: AiSector,
        evaluateFeedback: EvaluateFeedback,
        selected: Boolean,
    ) {
        feedback(
            aiSector = aiSector,
            evaluate = evaluateFeedback,
            articleType = ArticleType.FirstDraft,
            selected = selected
        )
    }

    //    /**
//     * 二稿提交反馈
//     */
    fun secondDraftFeedback(
        aiSector: AiSector,
        evaluateFeedback: EvaluateFeedback,
        selected: Boolean,
    ) {
        feedback(
            aiSector = aiSector,
            evaluate = evaluateFeedback,
            articleType = ArticleType.SecondDraft,
            selected = selected
        )
    }

    private fun feedback(
        aiSector: AiSector,
        evaluate: EvaluateFeedback,
        articleType: ArticleType,
        selected: Boolean,
    ) {
        viewModelScope.launchOnHttp {
            var needAnimation = false
            if (selected) {
                val result = compositionRepository.addFeedbackEvaluate(
                    workId = curWorkId,
                    workStateId = curWorkStateId,
                    feedbackType = aiSector.feedbackType,
                    evaluateType = evaluate.evaluateType,
                    articleType = articleType
                )
                if (evaluate == EvaluateFeedback.Like && result.isSuccess) {
                    needAnimation = true
                }
            } else {
                compositionRepository.cancelFeedbackEvaluate(
                    workId = curWorkId,
                    workStateId = curWorkStateId,
                    feedbackType = aiSector.feedbackType,
                    articleType = articleType
                )
            }

            compositionRepository.getStudentFeedbackEvaluate(
                workId = curWorkId,
                feedbackType = aiSector.feedbackType,
                articleType = articleType
            )
            refreshFeedbackEvaluate(aiSector, needAnimation, articleType)
        }
    }

    fun refreshFeedbackEvaluate(
        aiSector: AiSector,
        needAnimation: Boolean,
        articleType: ArticleType,
    ) {
        val evaluateType = compositionRepository.getStudentFeedbackEvaluate(
            workId = curWorkId,
            feedbackType = aiSector.feedbackType,
            articleType = articleType
        )
        val flow = if (articleType == ArticleType.FirstDraft) {
            studentFeedbackState
        } else {
            studentSecondFeedbackState
        }
        flow.update {
            it.copy(
                feedbackMap = it.feedbackMap.toMutableMap().apply {
                    when (evaluateType) {
                        EvaluateType.Like -> {
                            set(
                                key = aiSector,
                                value = EvaluateFeedback.Like
                            )
                        }

                        EvaluateType.Dislike -> {
                            set(
                                key = aiSector,
                                value = EvaluateFeedback.Dislike
                            )
                        }

                        else -> {
                            remove(aiSector)
                        }
                    }
                },
                needAnimation = needAnimation
            )
        }
    }

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    private fun checkOcrJob(createTimeMillis: Long, type: Int) {
        ocrCreateTimeMillis = createTimeMillis
        // 若之前不是 内容OCR，则需要自动切换页面，否则不自动切换
        val contentOcrLoading =
            (this.aiResponseState.value as? AiResponseState.OcrLoading)?.isTopic == false
        // 更新状态
        updateOcrProgress(type)
        if (!contentOcrLoading && type == AiStreamType.First.value) {
            HLog.i(
                HTag.TAG,
                "当前内容OCR识别中, 最近一个状态不是内容OCR识别中，自动切换到 AiGuidance"
            )
            stepManager.switchStep(CompositionStep.AiGuidance)
        }
        ThreadPlugins.runOnWorkThread(requestAiStreamStatusTask, 3_000L)
    }

    private fun updateOcrProgress(type: Int) {
        val second: Int = ((System.currentTimeMillis() - ocrCreateTimeMillis) / 1000L).toInt()
        val progress = when {
            second <= 0 -> {
                0
            }

            second <= 5 -> { // 前 25%，每秒前进 5%
                minOf(second * 5, 25)
            }

            second < 29 -> { // 25 ~ 97 每秒 % 3
                minOf(25 + (second - 5) * 3, 97)
            }

            else -> {
                97
            }
        }
        updateAiJudgeState(
            AiResponseState.OcrLoading(
                progress = progress,
                isTopic = type == AiStreamType.Title.value
            )
        )
        ThreadPlugins.runOnWorkThread(ocrLoadingTask, 1_000L)
    }

    private fun ocrJobFailed(compositionStatusResponse: CompositionStatusResponse?, errorMessage:
    String?, type: Int) {
        ThreadPlugins.removeWorkCallbacks(ocrLoadingTask)
        if (type == AiStreamType.Title.value) {
            updateAiJudgeState(AiResponseState.TitleOcrFailed( compositionStatusResponse =
                compositionStatusResponse,errorMessage = errorMessage))
        } else {
            switchStep(CompositionStep.AiGuidance)
            updateAiJudgeState(AiResponseState.ContentOcrFailed(errorMessage = errorMessage))
        }
    }

    private fun finishOcrJob(type: Int) {
        ThreadPlugins.removeWorkCallbacks(ocrLoadingTask)
        ThreadPlugins.removeWorkCallbacks(waitingAiBackgroundResultTask)
        switchStep(CompositionStep.AiGuidance)
        updateAiJudgeState(
            AiResponseState.OcrLoading(
                progress = 100,
                isTopic = type == AiStreamType.Title.value
            )
        )
    }

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    override fun onCleared() {
        super.onCleared()
        stopLoopAiResult()
        ThreadPlugins.removeWorkCallbacks(ocrLoadingTask)
        aiStreamClient.closeAllFlows()
        cachedAiStreamJob?.cancel()
    }

    fun markSubmitSuccess() {
        stepManager.updateWorkState(WorkStatus.ALL_SUBMITTED)
        ThreadPlugins.runOnWorkThread(requestAiStreamStatusTask)
        /**
         *此时我在此处修改 cachedSelectedAiSector: AiSector = AiSector.Evaluate 的值 为默认值
         */
        cachedSelectedAiSector = AiSector.Evaluate
//        stepManager.switchStep(CompositionStep.CheckSecondPractice)
        updateCompositionDiffState()
    }


    // ----------------------------------------------------------------------
    fun reportAiTabSwitchEvent(
        compositionStep: CompositionStep? = compositionStepState.value,
        aiSector: AiSector = cachedSelectedAiSector,
    ) {
        if (aiStepStartTimeMillis > 0 && compositionStep != null) {
            if (compositionStep.step <= CompositionStep.SubmitPractice.step) {
                // 作答前的步骤
                BusinessBury.studentCompositionDoWorkEvent(
                    workId = curWorkId,
                    workStateId = curWorkStateId,
                    stepName = compositionStep.name,
                    aiTab = aiSector.name,
                    startTimeMillis = aiStepStartTimeMillis
                )
            } else if (compositionStep is CompositionStep.CheckFirstPractice
                || compositionStep is CompositionStep.CheckSecondPractice
            ) { // 查看作业的步骤
                BusinessBury.studentCompositionCheckWork(
                    workId = curWorkId,
                    workStateId = curWorkStateId,
                    stepName = compositionStep.name,
                    aiTab = aiSector.name,
                    startTimeMillis = aiStepStartTimeMillis
                )
            }
        }
        resetAiStepTimeCounting(compositionStep)
    }

    private fun resetAiStepTimeCounting(compositionStep: CompositionStep?) {
        aiStepStartTimeMillis = if (compositionStep is CompositionStep.AiGuidance
            || compositionStep is CompositionStep.SecondPractice
            || compositionStep is CompositionStep.CheckFirstPractice
            || compositionStep is CompositionStep.CheckSecondPractice
        ) {
            System.currentTimeMillis()
        } else { // 切换到不包含 AI 模块的步骤，重置
            -1L
        }
    }

    fun reportSwitchStepEvent() {
        if (stepStartTime > 0) {
            if (compositionStepState.value.step > CompositionStep.SubmitPractice.step) { // 全部提交，标识查看
                BusinessBury.studentCompositionCheckWork(
                    workId = curWorkId,
                    workStateId = curWorkStateId,
                    stepName = compositionStepState.value.name,
                    startTimeMillis = stepStartTime
                )
            } else {
                BusinessBury.studentCompositionDoWorkEvent(
                    workId = curWorkId,
                    workStateId = curWorkStateId,
                    stepName = compositionStepState.value.name,
                    startTimeMillis = stepStartTime
                )
            }

        }
        stepStartTime = System.currentTimeMillis()
    }

    fun resetCounting(compositionStep: CompositionStep? = compositionStepState.value) {
        stepStartTime = System.currentTimeMillis()
        timeCountStart = SystemClock.uptimeMillis()
        resetAiStepTimeCounting(compositionStep)
    }
}